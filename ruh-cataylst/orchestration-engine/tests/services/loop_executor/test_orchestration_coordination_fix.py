"""
Test Orchestration Coordination Fix for Loop Executor

This module tests the fix for the loop executor state management issue where
transitions with complex dependencies (A->B,C,D where B,C->D) were not
properly waiting for dependencies to complete.

The fix ensures that loop executor coordinates with orchestration engine
instead of bypassing dependency management.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, call
from app.services.loop_executor.loop_executor import LoopExecutor
from app.core_.state_manager import WorkflowStateManager


class TestOrchestrationCoordinationFix:
    """Test the orchestration coordination fix for complex dependencies."""

    @pytest.fixture
    def mock_orchestration_engine(self):
        """Create a mock orchestration engine with proper state management."""
        engine = Mock()
        
        # Mock state manager with proper dependency tracking
        state_manager = Mock(spec=WorkflowStateManager)
        state_manager.pending_transitions = set()
        state_manager.waiting_transitions = set()
        state_manager.completed_transitions = set()
        state_manager.transition_results = {}
        
        # Mock dependency map for complex scenario: A->B,C,D where B,C->D
        dependency_map = {
            "A": [],  # No dependencies
            "B": ["A"],  # Depends on A
            "C": ["A"],  # Depends on A  
            "D": ["A", "B", "C"]  # Depends on A, B, and C
        }
        
        engine.state_manager = state_manager
        engine.dependency_map = dependency_map
        engine.transitions_by_id = {
            "A": {"id": "A", "next_transitions": ["B", "C", "D"]},
            "B": {"id": "B", "next_transitions": ["D"]},
            "C": {"id": "C", "next_transitions": ["D"]},
            "D": {"id": "D", "next_transitions": []}
        }
        
        # Mock transition handler
        transition_handler = Mock()
        transition_handler._execute_transition_with_tracking = AsyncMock()
        engine.transition_handler = transition_handler
        
        return engine

    @pytest.fixture
    def loop_executor(self, mock_orchestration_engine):
        """Create a loop executor with mocked dependencies."""
        executor = LoopExecutor(
            state_manager=Mock(),
            workflow_utils=Mock(),
            result_callback=AsyncMock(),
            transitions_by_id=mock_orchestration_engine.transitions_by_id,
            nodes={},
            transition_handler=Mock(),
            user_id="test_user"
        )
        
        # Set the orchestration engine
        executor.orchestration_engine = mock_orchestration_engine
        
        return executor

    @pytest.mark.asyncio
    async def test_complex_dependency_coordination(self, loop_executor, mock_orchestration_engine):
        """
        Test that complex dependencies (A->B,C,D where B,C->D) are properly coordinated.
        
        This test verifies that:
        1. Transition A is added to pending queue first
        2. When A completes, B and C are added to pending, D to waiting
        3. When B and C complete, D is moved from waiting to pending
        4. D only executes after B and C have completed
        """
        
        # Setup: Mock the completion sequence
        completion_sequence = []
        
        def track_completion(transition_id):
            completion_sequence.append(f"completed_{transition_id}")
            mock_orchestration_engine.state_manager.completed_transitions.add(transition_id)
            
            # Simulate transition results
            mock_orchestration_engine.state_manager.transition_results[transition_id] = {
                "test_tool": {"result": f"result_from_{transition_id}"}
            }
        
        # Test the coordination method directly
        entry_transitions = ["A"]
        exit_transitions = ["D"]
        
        # Step 1: Add entry transitions to orchestration queue
        await loop_executor._add_transitions_to_orchestration_queue(
            entry_transitions,
            mock_orchestration_engine.dependency_map
        )
        
        # Verify A was added to pending (no dependencies)
        assert "A" in mock_orchestration_engine.state_manager.pending_transitions
        assert "A" not in mock_orchestration_engine.state_manager.waiting_transitions
        
        # Step 2: Simulate A completion and process next transitions
        track_completion("A")
        next_transitions_A = ["B", "C", "D"]
        
        await loop_executor._add_transitions_to_orchestration_queue(
            next_transitions_A,
            mock_orchestration_engine.dependency_map
        )
        
        # Verify B and C are pending (only depend on A), D is waiting (depends on A,B,C)
        assert "B" in mock_orchestration_engine.state_manager.pending_transitions
        assert "C" in mock_orchestration_engine.state_manager.pending_transitions
        assert "D" in mock_orchestration_engine.state_manager.waiting_transitions
        assert "D" not in mock_orchestration_engine.state_manager.pending_transitions
        
        # Step 3: Simulate B completion
        track_completion("B")
        
        # D should still be waiting (C hasn't completed yet)
        await loop_executor._add_transitions_to_orchestration_queue(
            ["D"],  # B's next transition
            mock_orchestration_engine.dependency_map
        )
        
        assert "D" in mock_orchestration_engine.state_manager.waiting_transitions
        assert "D" not in mock_orchestration_engine.state_manager.pending_transitions
        
        # Step 4: Simulate C completion
        track_completion("C")
        
        # Now D should be moved to pending (all dependencies met)
        await loop_executor._add_transitions_to_orchestration_queue(
            ["D"],  # C's next transition
            mock_orchestration_engine.dependency_map
        )
        
        # Since D already exists in waiting, it should now be moved to pending
        # (This would be handled by orchestration engine's move_waiting_to_pending)
        # For this test, we'll simulate that behavior
        if all(dep in mock_orchestration_engine.state_manager.completed_transitions 
               for dep in mock_orchestration_engine.dependency_map["D"]):
            mock_orchestration_engine.state_manager.waiting_transitions.discard("D")
            mock_orchestration_engine.state_manager.pending_transitions.add("D")
        
        assert "D" in mock_orchestration_engine.state_manager.pending_transitions
        assert "D" not in mock_orchestration_engine.state_manager.waiting_transitions
        
        # Verify the completion sequence shows proper dependency order
        assert "completed_A" in completion_sequence
        assert "completed_B" in completion_sequence
        assert "completed_C" in completion_sequence

        # Verify A completed before B and C
        a_index = completion_sequence.index("completed_A")
        b_index = completion_sequence.index("completed_B")
        c_index = completion_sequence.index("completed_C")

        assert a_index < b_index
        assert a_index < c_index

    @pytest.mark.asyncio
    async def test_coordination_method_integration(self, loop_executor, mock_orchestration_engine):
        """
        Test the coordination method integration to ensure it properly coordinates with orchestration.

        This tests the core fix: that loop executor coordinates with orchestration engine
        instead of bypassing dependency management.
        """

        # Test the coordination method directly
        entry_transitions = ["A"]
        exit_transitions = ["D"]
        iteration_payload = "test_item"
        iteration_index = 0

        # Mock the completion monitoring to return immediately
        async def mock_wait_for_completion(*args, **kwargs):
            return {"test_tool": {"result": "coordination_result"}}

        loop_executor._wait_for_loop_body_completion = mock_wait_for_completion

        # Test the coordination
        result = await loop_executor._coordinate_loop_iteration_with_orchestration(
            entry_transitions,
            exit_transitions,
            iteration_payload,
            iteration_index
        )

        # Verify the result
        assert result is not None
        assert "test_tool" in result
        assert result["test_tool"]["result"] == "coordination_result"

        # Verify that entry transition was added to orchestration queue
        assert "A" in mock_orchestration_engine.state_manager.pending_transitions

    @pytest.mark.asyncio
    async def test_orchestration_idle_waiting(self, loop_executor, mock_orchestration_engine):
        """Test that loop executor waits for orchestration to be idle between iterations."""
        
        # Setup: Add some transitions to pending/waiting
        mock_orchestration_engine.state_manager.pending_transitions.add("test_pending")
        mock_orchestration_engine.state_manager.waiting_transitions.add("test_waiting")
        
        # Start the idle waiting in background
        wait_task = asyncio.create_task(loop_executor._wait_for_orchestration_idle())
        
        # Give it a moment to start waiting
        await asyncio.sleep(0.05)
        
        # Verify it's still waiting (not completed)
        assert not wait_task.done()
        
        # Clear pending transitions
        mock_orchestration_engine.state_manager.pending_transitions.clear()
        
        # Should still be waiting (waiting transitions remain)
        await asyncio.sleep(0.05)
        assert not wait_task.done()
        
        # Clear waiting transitions
        mock_orchestration_engine.state_manager.waiting_transitions.clear()
        
        # Now it should complete
        await asyncio.sleep(0.15)  # Give it time to detect idle state
        assert wait_task.done()
        
        # Clean up
        if not wait_task.done():
            wait_task.cancel()

    @pytest.mark.asyncio
    async def test_loop_state_manager_orchestration_tracking(self, loop_executor):
        """Test that loop state manager properly tracks orchestration coordination."""
        
        # Create a real loop state manager for this test
        from app.services.loop_executor.loop_state_manager import LoopStateManager
        loop_state_manager = LoopStateManager("test_loop", "test_transition")
        loop_executor.loop_state_manager = loop_state_manager
        
        # Test orchestration sync state management
        assert loop_state_manager.get_orchestration_sync_state() == "idle"
        
        loop_state_manager.set_orchestration_sync_state("coordinating")
        assert loop_state_manager.get_orchestration_sync_state() == "coordinating"
        
        loop_state_manager.set_orchestration_sync_state("waiting_completion")
        assert loop_state_manager.get_orchestration_sync_state() == "waiting_completion"
        
        # Test transition tracking
        loop_state_manager.add_pending_orchestration_transition("A")
        loop_state_manager.add_pending_orchestration_transition("B")
        
        pending = loop_state_manager.get_pending_orchestration_transitions()
        assert "A" in pending
        assert "B" in pending
        
        # Mark completion
        loop_state_manager.mark_orchestration_transition_completed("A")
        
        pending = loop_state_manager.get_pending_orchestration_transitions()
        completed = loop_state_manager.get_completed_orchestration_transitions()
        
        assert "A" not in pending
        assert "A" in completed
        assert "B" in pending
        
        # Test clearing
        loop_state_manager.clear_orchestration_tracking()
        
        assert len(loop_state_manager.get_pending_orchestration_transitions()) == 0
        assert len(loop_state_manager.get_completed_orchestration_transitions()) == 0
        assert loop_state_manager.get_orchestration_sync_state() == "idle"
