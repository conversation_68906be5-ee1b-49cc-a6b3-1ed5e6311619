2025-06-29 16:20:33 - Main - INFO - Starting Server
2025-06-29 16:20:33 - Main - INFO - Connection at: **************:9092
2025-06-29 16:20:33 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-29 16:20:33 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-29 16:20:33 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-29 16:20:33 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-29 16:20:33 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 16:20:34 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 16:20:34 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 16:20:36 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 16:20:38 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-29 16:20:38 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-29 16:20:40 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 16:20:41 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-29 16:20:41 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 16:20:42 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 16:20:42 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 16:20:44 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 16:20:44 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-29 16:20:44 - RedisEventListener - INFO - Redis event listener started
2025-06-29 16:20:44 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-29 16:20:44 - StateManager - DEBUG - Using provided database connections
2025-06-29 16:20:44 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 16:20:44 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 16:20:44 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 16:20:44 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-29 16:20:45 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 16:20:45 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 16:20:45 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-29 16:20:45 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-29 16:20:45 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-29 16:20:45 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-29 16:20:47 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-29 16:20:47 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-29 16:20:47 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-29 16:20:52 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-29 16:20:58 - Main - ERROR - Shutting down due to keyboard interrupt...
