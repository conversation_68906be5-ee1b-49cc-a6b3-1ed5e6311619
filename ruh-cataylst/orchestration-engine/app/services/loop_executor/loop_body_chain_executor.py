"""
Loop Body Chain Executor

Handles execution of loop body transition chains:
- loop -> a -> b -> c -> d -> loop (collect result from d)
- Coordinates with orchestration engine for actual transition execution
- Manages chain state and result collection
- Supports complex loop body workflows
"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Set
from app.utils.enhanced_logger import get_logger


class LoopBodyChainExecutor:
    """
    Executes and manages loop body transition chains.
    
    Handles the execution flow:
    1. Start loop body chain with iteration data
    2. Monitor chain execution progress
    3. Detect chain completion
    4. Collect results from chain end
    5. Return aggregated result to loop executor
    """

    def __init__(
        self,
        state_manager,
        transition_handler,
        workflow_utils,
        transitions_by_id: Dict[str, Any],
        user_id: str = None,
    ):
        self.state_manager = state_manager
        self.transition_handler = transition_handler
        self.workflow_utils = workflow_utils
        self.transitions_by_id = transitions_by_id
        self.user_id = user_id
        self.logger = get_logger(__name__)

        # Chain execution state
        self.active_chains: Dict[str, Dict[str, Any]] = {}
        self.chain_results: Dict[str, Any] = {}
        self.completion_callbacks: Dict[str, asyncio.Event] = {}

    async def execute_loop_body_chain(
        self,
        loop_transition_id: str,
        iteration_index: int,
        iteration_item: Any,
        iteration_context: Dict[str, Any],
        loop_body_config: Dict[str, Any],
    ) -> Any:
        """
        Execute a loop body chain for a single iteration.

        Args:
            loop_transition_id: ID of the loop transition
            iteration_index: Index of the current iteration
            iteration_item: Data item for this iteration
            iteration_context: Context data for this iteration
            loop_body_config: Loop body configuration

        Returns:
            Result from the completed loop body chain
        """
        chain_id = f"{loop_transition_id}_iteration_{iteration_index}"
        
        self.logger.info(
            f"🔄 Starting loop body chain execution: {chain_id}"
        )

        try:
            # Initialize chain state
            await self._initialize_chain_state(
                chain_id, loop_transition_id, iteration_index, 
                iteration_item, iteration_context, loop_body_config
            )

            # Start chain execution
            await self._start_chain_execution(chain_id)

            # Wait for chain completion
            result = await self._wait_for_chain_completion(chain_id)

            self.logger.info(
                f"✅ Loop body chain completed: {chain_id}, result: {result}"
            )

            return result

        except Exception as e:
            self.logger.error(
                f"❌ Loop body chain execution failed: {chain_id}, error: {str(e)}"
            )
            raise

        finally:
            # Cleanup chain state
            await self._cleanup_chain_state(chain_id)

    async def _initialize_chain_state(
        self,
        chain_id: str,
        loop_transition_id: str,
        iteration_index: int,
        iteration_item: Any,
        iteration_context: Dict[str, Any],
        loop_body_config: Dict[str, Any],
    ) -> None:
        """Initialize state for a loop body chain execution."""
        
        entry_transitions = loop_body_config.get("entry_transitions", [])
        exit_transitions = loop_body_config.get("exit_transitions", [])
        
        if not entry_transitions:
            # Auto-detect entry transitions from loop output connections
            entry_transitions = await self._auto_detect_entry_transitions(loop_transition_id)
        
        if not exit_transitions:
            # Auto-detect exit transitions from chain analysis
            exit_transitions = await self._auto_detect_exit_transitions(entry_transitions)

        self.active_chains[chain_id] = {
            "loop_transition_id": loop_transition_id,
            "iteration_index": iteration_index,
            "iteration_item": iteration_item,
            "iteration_context": iteration_context,
            "entry_transitions": entry_transitions,
            "exit_transitions": exit_transitions,
            "completed_transitions": set(),
            "pending_transitions": set(entry_transitions),
            "chain_state": "initializing",
            "start_time": time.time(),
            "result": None,
        }

        # Create completion event
        self.completion_callbacks[chain_id] = asyncio.Event()

        self.logger.debug(
            f"🔧 Initialized chain state for {chain_id}: "
            f"entry={entry_transitions}, exit={exit_transitions}"
        )

    async def _start_chain_execution(self, chain_id: str) -> None:
        """Start execution of the loop body chain."""
        
        chain_state = self.active_chains[chain_id]
        entry_transitions = chain_state["entry_transitions"]
        iteration_item = chain_state["iteration_item"]
        iteration_context = chain_state["iteration_context"]

        # Inject iteration data into state manager for entry transitions
        await self._inject_iteration_data_for_chain(
            chain_id, iteration_item, iteration_context
        )

        # Mark chain as running
        chain_state["chain_state"] = "running"

        # Start entry transitions through orchestration engine
        for transition_id in entry_transitions:
            self.logger.debug(
                f"🚀 Starting entry transition: {transition_id} for chain {chain_id}"
            )

            # Register for completion notification first
            await self._register_transition_completion_callback(
                chain_id, transition_id
            )

            # Execute the transition through the orchestration engine and capture next transitions
            next_transitions = await self._execute_transition_through_orchestration(transition_id)

            # Process any immediate next transitions returned from the execution
            if next_transitions:
                self.logger.debug(
                    f"🔗 Entry transition {transition_id} returned next transitions: {next_transitions}"
                )
                # These will be handled by the completion callback system

        self.logger.info(
            f"🔄 Started loop body chain execution: {chain_id} with {len(entry_transitions)} entry transitions"
        )

    async def _wait_for_chain_completion(self, chain_id: str, timeout: Optional[int] = None) -> Any:
        """Wait for the loop body chain to complete and return the result."""

        completion_event = self.completion_callbacks[chain_id]
        chain_state = self.active_chains[chain_id]

        # Use provided timeout or default
        if timeout is None:
            timeout = chain_state.get("timeout", 300)  # 5 minutes default

        start_time = time.time()

        try:
            await asyncio.wait_for(completion_event.wait(), timeout=timeout)

            # Check if chain was cancelled
            if chain_state.get("chain_state") == "cancelled":
                reason = chain_state.get("cancellation_reason", "Unknown reason")
                self.logger.warning(f"⚠️ Chain {chain_id} was cancelled: {reason}")
                raise asyncio.CancelledError(f"Chain execution cancelled: {reason}")

            # Chain completed, extract result
            result = chain_state.get("result")
            execution_time = time.time() - start_time

            if result is None:
                self.logger.warning(
                    f"⚠️ Chain {chain_id} completed but no result found (execution time: {execution_time:.2f}s)"
                )
                return None

            self.logger.info(
                f"✅ Chain {chain_id} completed successfully (execution time: {execution_time:.2f}s)"
            )
            return result

        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            self.logger.error(
                f"⏰ Chain {chain_id} execution timed out after {timeout} seconds (actual time: {execution_time:.2f}s)"
            )

            # Mark chain as timed out
            chain_state["chain_state"] = "timeout"
            chain_state["timeout_duration"] = execution_time

            # Log chain status for debugging
            self._log_chain_timeout_details(chain_id, chain_state)

            raise asyncio.TimeoutError(f"Chain {chain_id} execution timed out after {timeout} seconds")

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(
                f"❌ Chain {chain_id} execution failed after {execution_time:.2f}s: {str(e)}"
            )

            # Mark chain as failed
            chain_state["chain_state"] = "failed"
            chain_state["error"] = str(e)
            chain_state["execution_time"] = execution_time

            raise

    def _log_chain_timeout_details(self, chain_id: str, chain_state: Dict[str, Any]) -> None:
        """Log detailed information about a timed-out chain for debugging."""

        completed_count = len(chain_state.get("completed_transitions", set()))
        pending_count = len(chain_state.get("pending_transitions", set()))

        self.logger.error(
            f"🔍 Chain {chain_id} timeout details:\n"
            f"  - State: {chain_state.get('chain_state', 'unknown')}\n"
            f"  - Completed transitions: {completed_count}\n"
            f"  - Pending transitions: {pending_count}\n"
            f"  - Start time: {chain_state.get('start_time', 'unknown')}\n"
            f"  - Iteration index: {chain_state.get('iteration_index', 'unknown')}"
        )

        # Log pending transitions for debugging
        pending_transitions = chain_state.get("pending_transitions", set())
        if pending_transitions:
            self.logger.error(
                f"  - Pending transition IDs: {list(pending_transitions)}"
            )

    async def _auto_detect_entry_transitions(self, loop_transition_id: str) -> List[str]:
        """Auto-detect entry transitions from loop output connections."""
        
        loop_transition = self.transitions_by_id.get(loop_transition_id)
        if not loop_transition:
            return []

        entry_transitions = []
        output_data_configs = loop_transition.get("node_info", {}).get("output_data", [])

        for config in output_data_configs:
            # Look for iteration output connections (not exit connections)
            handle_mappings = config.get("output_handle_registry", {}).get("handle_mappings", [])
            
            for mapping in handle_mappings:
                handle_type = mapping.get("handle_type", "standard")
                result_path = mapping.get("result_path", "").lower()
                
                # Check if this is a loop body entry connection
                if (
                    handle_type == "loop_body_entry" or
                    "current_item" in result_path or
                    "iteration" in result_path
                ):
                    to_transition_id = config.get("to_transition_id")
                    if to_transition_id and to_transition_id not in entry_transitions:
                        entry_transitions.append(to_transition_id)

        self.logger.debug(
            f"🔍 Auto-detected entry transitions for {loop_transition_id}: {entry_transitions}"
        )
        
        return entry_transitions

    async def _auto_detect_exit_transitions(self, entry_transitions: List[str]) -> List[str]:
        """Auto-detect exit transitions by analyzing the chain structure."""

        exit_transitions = []
        visited_transitions = set()

        # Perform chain analysis starting from entry transitions
        for entry_transition_id in entry_transitions:
            chain_exits = await self._analyze_chain_from_entry(
                entry_transition_id, visited_transitions
            )
            exit_transitions.extend(chain_exits)

        # Remove duplicates while preserving order
        unique_exits = []
        for exit_id in exit_transitions:
            if exit_id not in unique_exits:
                unique_exits.append(exit_id)

        # If no exits found through analysis, fall back to simple heuristic
        if not unique_exits and entry_transitions:
            self.logger.warning(
                "Chain analysis found no exit transitions, using entry transitions as fallback"
            )
            unique_exits = entry_transitions.copy()

        self.logger.debug(
            f"🔍 Auto-detected exit transitions: {unique_exits}"
        )

        return unique_exits

    async def _analyze_chain_from_entry(
        self, entry_transition_id: str, visited_transitions: Set[str]
    ) -> List[str]:
        """Analyze chain structure starting from an entry transition to find exits."""

        if entry_transition_id in visited_transitions:
            return []  # Avoid infinite loops

        visited_transitions.add(entry_transition_id)

        transition = self.transitions_by_id.get(entry_transition_id)
        if not transition:
            return []

        # Get outgoing connections from this transition
        outgoing_connections = self._get_outgoing_connections(transition)

        # If no outgoing connections, this is an exit transition
        if not outgoing_connections:
            return [entry_transition_id]

        # Check if any outgoing connections lead back to the loop (exit condition)
        loop_connections = []
        chain_connections = []

        for connection in outgoing_connections:
            target_transition_id = connection.get("to_transition_id")
            handle_type = connection.get("handle_type", "standard")

            # Check if this connection goes back to loop or to another chain transition
            if handle_type in ["loop_exit", "loop_body_exit"] or self._is_loop_connection(connection):
                loop_connections.append(target_transition_id)
            else:
                chain_connections.append(target_transition_id)

        # If there are loop connections, this transition is an exit
        exit_transitions = []
        if loop_connections:
            exit_transitions.append(entry_transition_id)

        # Recursively analyze chain connections
        for chain_transition_id in chain_connections:
            chain_exits = await self._analyze_chain_from_entry(
                chain_transition_id, visited_transitions
            )
            exit_transitions.extend(chain_exits)

        return exit_transitions

    def _get_outgoing_connections(self, transition: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get all outgoing connections from a transition."""

        connections = []
        node_info = transition.get("node_info", {})
        output_data_configs = node_info.get("output_data", [])

        for config in output_data_configs:
            to_transition_id = config.get("to_transition_id")
            if to_transition_id:
                # Extract handle information
                handle_registry = config.get("output_handle_registry", {})
                handle_mappings = handle_registry.get("handle_mappings", [])

                for mapping in handle_mappings:
                    connection = {
                        "to_transition_id": to_transition_id,
                        "handle_type": mapping.get("handle_type", "standard"),
                        "handle_id": mapping.get("handle_id"),
                        "result_path": mapping.get("result_path", ""),
                    }
                    connections.append(connection)

        return connections

    def _is_loop_connection(self, connection: Dict[str, Any]) -> bool:
        """Check if a connection leads back to the loop node."""

        target_transition_id = connection.get("to_transition_id")
        if not target_transition_id:
            return False

        target_transition = self.transitions_by_id.get(target_transition_id)
        if not target_transition:
            return False

        # Check if target transition is a loop transition
        execution_type = target_transition.get("execution_type", "")
        return execution_type == "loop"

    async def _inject_iteration_data_for_chain(
        self, chain_id: str, iteration_item: Any, iteration_context: Dict[str, Any]
    ) -> None:
        """Inject iteration data into state manager for chain execution."""
        
        # Create iteration data that chain transitions can access
        iteration_data = {
            "current_item": iteration_item,
            "iteration_context": iteration_context,
            "chain_id": chain_id,
        }

        # Store with a key that chain transitions can access
        iteration_data_key = f"chain_{chain_id}_iteration_data"
        if self.state_manager:
            self.state_manager.store_result(iteration_data_key, iteration_data)

        self.logger.debug(
            f"💾 Injected iteration data for chain {chain_id}: {iteration_data}"
        )

    async def _register_transition_completion_callback(
        self, chain_id: str, transition_id: str
    ) -> None:
        """Register to be notified when a transition completes."""

        # Register this chain executor to receive completion notifications
        # The orchestration engine will call notify_transition_completion when transitions complete

        # Store the mapping of transition to chain for quick lookup
        if not hasattr(self, '_transition_to_chain_mapping'):
            self._transition_to_chain_mapping = {}

        self._transition_to_chain_mapping[transition_id] = chain_id

        # If we have a transition handler, register with it
        if hasattr(self.transition_handler, 'register_completion_callback'):
            await self.transition_handler.register_completion_callback(
                transition_id, self._on_transition_complete
            )

        self.logger.debug(
            f"📝 Registered completion callback for transition {transition_id} in chain {chain_id}"
        )

    async def _execute_transition_through_orchestration(self, transition_id: str) -> List[str]:
        """Execute a transition through the orchestration engine and return next transitions."""

        try:
            # Get the transition configuration
            transition = self.transitions_by_id.get(transition_id)
            if not transition:
                self.logger.error(f"Transition {transition_id} not found")
                return []

            # Execute the transition through the transition handler
            # This will trigger the normal orchestration flow and return next transitions
            next_transitions = await self.transition_handler._execute_transition_with_tracking(transition)

            self.logger.debug(
                f"✅ Executed transition {transition_id} through orchestration engine, next_transitions: {next_transitions}"
            )

            return next_transitions or []

        except Exception as e:
            self.logger.error(
                f"❌ Failed to execute transition {transition_id} through orchestration: {str(e)}"
            )
            return []

    async def _on_transition_complete(self, transition_id: str, result: Any) -> None:
        """Callback method called when a transition completes."""

        # Find the chain this transition belongs to
        chain_id = getattr(self, '_transition_to_chain_mapping', {}).get(transition_id)

        if chain_id:
            await self._handle_transition_completion(chain_id, transition_id, result)
        else:
            self.logger.warning(
                f"⚠️ Received completion notification for unknown transition: {transition_id}"
            )

    async def _cleanup_chain_state(self, chain_id: str) -> None:
        """Clean up state for a completed chain."""

        # Remove from active chains
        if chain_id in self.active_chains:
            del self.active_chains[chain_id]

        # Remove completion callback
        if chain_id in self.completion_callbacks:
            del self.completion_callbacks[chain_id]

        # Clean up transition to chain mapping
        if hasattr(self, '_transition_to_chain_mapping'):
            transitions_to_remove = [
                tid for tid, cid in self._transition_to_chain_mapping.items()
                if cid == chain_id
            ]
            for transition_id in transitions_to_remove:
                del self._transition_to_chain_mapping[transition_id]

        # Clean up any temporary state data
        iteration_data_key = f"chain_{chain_id}_iteration_data"
        try:
            # Remove iteration data from state manager if available
            if self.state_manager and hasattr(self.state_manager, 'remove_result'):
                self.state_manager.remove_result(iteration_data_key)
            elif self.state_manager and hasattr(self.state_manager, 'delete_state'):
                await self.state_manager.delete_state(iteration_data_key)
        except Exception as e:
            self.logger.debug(f"Could not clean up state data for {iteration_data_key}: {e}")

        self.logger.debug(f"🧹 Cleaned up chain state for {chain_id}")

    def notify_transition_completion(
        self, transition_id: str, result: Any
    ) -> None:
        """
        Notify the chain executor that a transition has completed.
        
        This method should be called by the orchestration engine when
        transitions complete.
        """
        
        # Find which chain this transition belongs to
        for chain_id, chain_state in self.active_chains.items():
            if transition_id in chain_state.get("pending_transitions", set()):
                asyncio.create_task(
                    self._handle_transition_completion(chain_id, transition_id, result)
                )
                break

    async def _handle_transition_completion(
        self, chain_id: str, transition_id: str, result: Any
    ) -> None:
        """Handle completion of a transition in a chain."""

        chain_state = self.active_chains.get(chain_id)
        if not chain_state:
            self.logger.warning(f"⚠️ Received completion for unknown chain: {chain_id}")
            return

        # Mark transition as completed
        chain_state["completed_transitions"].add(transition_id)
        chain_state["pending_transitions"].discard(transition_id)

        # Store intermediate results for potential aggregation
        if "intermediate_results" not in chain_state:
            chain_state["intermediate_results"] = {}
        chain_state["intermediate_results"][transition_id] = result

        self.logger.debug(
            f"✅ Transition {transition_id} completed in chain {chain_id}"
        )

        # Check if this is an exit transition
        if transition_id in chain_state["exit_transitions"]:
            await self._handle_exit_transition_completion(chain_id, transition_id, result)
        else:
            # Check if we need to trigger next transitions in the chain
            await self._check_and_trigger_next_transitions(chain_id, transition_id, result)

    async def _handle_exit_transition_completion(
        self, chain_id: str, transition_id: str, result: Any
    ) -> None:
        """Handle completion of an exit transition."""

        chain_state = self.active_chains[chain_id]

        # Store the result from the exit transition
        # The main loop executor will handle aggregation across iterations
        chain_state["result"] = result

        chain_state["chain_state"] = "completed"

        # Signal completion
        completion_event = self.completion_callbacks.get(chain_id)
        if completion_event:
            completion_event.set()

        self.logger.info(
            f"🏁 Chain {chain_id} completed with exit transition {transition_id}"
        )

    async def _check_and_trigger_next_transitions(
        self, chain_id: str, completed_transition_id: str, result: Any
    ) -> None:
        """Check if completion of a transition should trigger next transitions."""

        chain_state = self.active_chains.get(chain_id)
        if not chain_state:
            return

        self.logger.debug(
            f"🔄 Checking for next transitions after {completed_transition_id} in chain {chain_id}"
        )

        # Get the completed transition configuration
        completed_transition = self.transitions_by_id.get(completed_transition_id)
        if not completed_transition:
            self.logger.warning(f"⚠️ Completed transition {completed_transition_id} not found in transitions")
            return

        # Find next transitions based on the completed transition's next_transitions
        next_transition_ids = completed_transition.get("next_transitions", [])

        if not next_transition_ids:
            self.logger.debug(f"No next transitions defined for {completed_transition_id}")
            return

        # Filter next transitions to only include those that are part of this chain
        chain_transitions = chain_state["entry_transitions"] | chain_state["exit_transitions"]
        relevant_next_transitions = [
            tid for tid in next_transition_ids
            if tid in chain_transitions and tid not in chain_state["completed_transitions"]
        ]

        if not relevant_next_transitions:
            self.logger.debug(f"No relevant next transitions found for {completed_transition_id} in chain {chain_id}")
            return

        # Execute the next transitions through the orchestration engine
        for next_transition_id in relevant_next_transitions:
            try:
                self.logger.debug(
                    f"🚀 Triggering next transition: {next_transition_id} after {completed_transition_id}"
                )

                # Register for completion notification
                await self._register_transition_completion_callback(chain_id, next_transition_id)

                # Add to pending transitions
                chain_state["pending_transitions"].add(next_transition_id)

                # Execute through orchestration engine and capture any further next transitions
                further_next_transitions = await self._execute_transition_through_orchestration(next_transition_id)

                # Log any further next transitions that might be returned
                if further_next_transitions:
                    self.logger.debug(
                        f"🔗 Transition {next_transition_id} returned further next transitions: {further_next_transitions}"
                    )

            except Exception as e:
                self.logger.error(
                    f"❌ Failed to trigger next transition {next_transition_id}: {str(e)}"
                )
                # Remove from pending if execution failed
                chain_state["pending_transitions"].discard(next_transition_id)



    def get_active_chains_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all active chains for monitoring."""

        status = {}
        for chain_id, chain_state in self.active_chains.items():
            status[chain_id] = {
                "state": chain_state.get("chain_state", "unknown"),
                "iteration_index": chain_state.get("iteration_index"),
                "completed_transitions": len(chain_state.get("completed_transitions", set())),
                "pending_transitions": len(chain_state.get("pending_transitions", set())),
                "start_time": chain_state.get("start_time"),
                "elapsed_time": time.time() - chain_state.get("start_time", time.time()),
                "has_result": chain_state.get("result") is not None
            }

        return status

    async def monitor_chain_health(self, chain_id: str) -> Dict[str, Any]:
        """Monitor the health and progress of a chain execution."""

        if chain_id not in self.active_chains:
            return {"status": "not_found", "error": f"Chain {chain_id} not found"}

        chain_state = self.active_chains[chain_id]
        current_time = time.time()
        start_time = chain_state.get("start_time", current_time)
        elapsed_time = current_time - start_time

        # Calculate progress metrics
        completed_transitions = chain_state.get("completed_transitions", set())
        pending_transitions = chain_state.get("pending_transitions", set())
        entry_transitions = chain_state.get("entry_transitions", set())
        exit_transitions = chain_state.get("exit_transitions", set())

        total_transitions = len(entry_transitions | exit_transitions)
        completed_count = len(completed_transitions)
        pending_count = len(pending_transitions)

        progress_percentage = (completed_count / total_transitions * 100) if total_transitions > 0 else 0

        # Determine health status
        health_status = "healthy"
        warnings = []

        # Check for stalled execution
        if elapsed_time > 60 and pending_count > 0:  # Stalled for more than 1 minute
            health_status = "warning"
            warnings.append(f"Chain has been running for {elapsed_time:.1f}s with {pending_count} pending transitions")

        # Check for timeout approaching
        timeout = chain_state.get("timeout", 300)
        if elapsed_time > timeout * 0.8:  # 80% of timeout reached
            health_status = "warning"
            warnings.append(f"Chain approaching timeout ({elapsed_time:.1f}s / {timeout}s)")

        # Check for failed state
        if chain_state.get("chain_state") in ["failed", "timeout", "cancelled"]:
            health_status = "critical"

        return {
            "status": health_status,
            "chain_id": chain_id,
            "chain_state": chain_state.get("chain_state", "unknown"),
            "progress": {
                "completed_transitions": completed_count,
                "pending_transitions": pending_count,
                "total_transitions": total_transitions,
                "progress_percentage": round(progress_percentage, 2)
            },
            "timing": {
                "elapsed_time": round(elapsed_time, 2),
                "timeout": timeout,
                "time_remaining": max(0, timeout - elapsed_time)
            },
            "warnings": warnings,
            "iteration_index": chain_state.get("iteration_index"),
            "has_result": chain_state.get("result") is not None
        }

    async def cancel_chain(self, chain_id: str, reason: str = "Manual cancellation") -> bool:
        """Cancel an active chain execution."""

        if chain_id not in self.active_chains:
            self.logger.warning(f"⚠️ Cannot cancel unknown chain: {chain_id}")
            return False

        chain_state = self.active_chains[chain_id]
        chain_state["chain_state"] = "cancelled"
        chain_state["cancellation_reason"] = reason

        # Signal completion with cancellation
        completion_event = self.completion_callbacks.get(chain_id)
        if completion_event:
            completion_event.set()

        self.logger.info(f"🚫 Cancelled chain {chain_id}: {reason}")

        # Clean up the chain
        await self._cleanup_chain_state(chain_id)

        return True

    async def get_chain_result(self, chain_id: str) -> Optional[Any]:
        """Get the result of a completed chain."""

        chain_state = self.active_chains.get(chain_id)
        if not chain_state:
            return None

        return chain_state.get("result")

    async def cleanup_stalled_chains(self, max_age_seconds: int = 600) -> List[str]:
        """Clean up chains that have been running for too long without progress."""

        current_time = time.time()
        stalled_chains = []

        for chain_id, chain_state in list(self.active_chains.items()):
            start_time = chain_state.get("start_time", current_time)
            elapsed_time = current_time - start_time

            # Check if chain is stalled
            if elapsed_time > max_age_seconds:
                chain_status = chain_state.get("chain_state", "unknown")

                if chain_status in ["running", "pending"]:
                    self.logger.warning(
                        f"🧹 Cleaning up stalled chain {chain_id} (running for {elapsed_time:.1f}s)"
                    )

                    # Cancel the stalled chain
                    await self.cancel_chain(
                        chain_id,
                        f"Automatic cleanup - stalled for {elapsed_time:.1f}s"
                    )
                    stalled_chains.append(chain_id)

        if stalled_chains:
            self.logger.info(f"🧹 Cleaned up {len(stalled_chains)} stalled chains: {stalled_chains}")

        return stalled_chains

    async def get_chain_diagnostics(self, chain_id: str) -> Dict[str, Any]:
        """Get comprehensive diagnostics for a chain."""

        if chain_id not in self.active_chains:
            return {"error": f"Chain {chain_id} not found"}

        chain_state = self.active_chains[chain_id]
        health_info = await self.monitor_chain_health(chain_id)

        return {
            "chain_id": chain_id,
            "health": health_info,
            "state_details": {
                "chain_state": chain_state.get("chain_state"),
                "start_time": chain_state.get("start_time"),
                "iteration_index": chain_state.get("iteration_index"),
                "iteration_context": chain_state.get("iteration_context"),
                "entry_transitions": list(chain_state.get("entry_transitions", set())),
                "exit_transitions": list(chain_state.get("exit_transitions", set())),
                "completed_transitions": list(chain_state.get("completed_transitions", set())),
                "pending_transitions": list(chain_state.get("pending_transitions", set())),
                "intermediate_results": len(chain_state.get("intermediate_results", {})),
                "has_result": chain_state.get("result") is not None,
                "timeout": chain_state.get("timeout"),
                "error": chain_state.get("error"),
                "cancellation_reason": chain_state.get("cancellation_reason")
            }
        }

    def is_chain_completed(self, chain_id: str) -> bool:
        """Check if a chain has completed execution."""

        chain_state = self.active_chains.get(chain_id)
        if not chain_state:
            return False

        return chain_state.get("chain_state") in ["completed", "cancelled", "failed"]
