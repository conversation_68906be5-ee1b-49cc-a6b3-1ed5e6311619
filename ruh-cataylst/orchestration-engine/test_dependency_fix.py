#!/usr/bin/env python3
"""
Test script to verify the loop executor dependency fix.
This tests that loop body transitions don't get stuck waiting for the main loop transition.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.loop_executor.loop_executor import LoopExecutor
from app.core_.state_manager import StateManager
from app.shared.redis_connections import RedisManager
from app.shared.postgres_connections import PostgresManager
from app.utils.enhanced_logger import setup_logging, get_logger

async def test_dependency_fix():
    """Test that the dependency fix works correctly."""
    
    # Setup logging
    setup_logging()
    logger = get_logger(__name__)
    
    logger.info("🧪 Testing loop executor dependency fix...")
    
    # Create mock orchestration engine with dependency map
    class MockOrchestrationEngine:
        def __init__(self):
            self.dependency_map = {
                'transition-LoopNode-123': ['transition-AgenticAI-456'],
                'transition-CombineText-789': ['transition-LoopNode-123', 'transition-AgenticAI-456'],
                'transition-MergeData-101': ['transition-LoopNode-123']
            }
            self.state_manager = MockStateManager()
    
    class MockStateManager:
        def __init__(self):
            self.completed_transitions = {'transition-AgenticAI-456'}
            self.pending_transitions = set()
            self.waiting_transitions = set()
    
    # Create loop executor
    loop_executor = LoopExecutor()
    loop_executor.transition_id = 'transition-LoopNode-123'
    loop_executor.orchestration_engine = MockOrchestrationEngine()
    loop_executor.logger = logger
    
    # Test the dependency map creation
    loop_body_transitions = ['transition-CombineText-789', 'transition-MergeData-101']
    original_dependency_map = loop_executor.orchestration_engine.dependency_map
    
    logger.info(f"📋 Original dependency map: {original_dependency_map}")
    
    # Create modified dependency map
    modified_map = loop_executor._create_loop_body_dependency_map(
        loop_body_transitions,
        original_dependency_map
    )
    
    logger.info(f"🔄 Modified dependency map: {modified_map}")
    
    # Verify the fix
    expected_modified_map = {
        'transition-LoopNode-123': ['transition-AgenticAI-456'],
        'transition-CombineText-789': ['transition-AgenticAI-456'],  # Loop dependency removed
        'transition-MergeData-101': []  # Loop dependency removed
    }
    
    if modified_map == expected_modified_map:
        logger.info("✅ Dependency fix test PASSED!")
        logger.info("🎯 Loop body transitions no longer depend on the main loop transition")
        return True
    else:
        logger.error("❌ Dependency fix test FAILED!")
        logger.error(f"Expected: {expected_modified_map}")
        logger.error(f"Got: {modified_map}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_dependency_fix())
    sys.exit(0 if result else 1)
